// @ts-nocheck
/**
 * Game Component for Kung Fu
 * Integrates all game elements and manages the main game loop
 */

import { useEffect, useRef, useState } from 'react';
import { GameEngine, GameState } from './engine';
import { Player } from './player';
import { Level, levelConfigs } from './level';

// Game states
enum GameScreenState {
  TITLE,
  LEVEL_INTRO,
  PLAYING,
  LEVEL_COMPLETE,
  GAME_OVER,
  VICTORY
}

const Game = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [gameScreenState, setGameScreenState] = useState<GameScreenState>(GameScreenState.TITLE);
  const [currentLevel, setCurrentLevel] = useState<number>(1);
  const [gameState, setGameState] = useState<GameState>({
    running: false,
    score: 0,
    lives: 3,
    level: 1,
    health: 100,
    maxHealth: 100
  });
  
  // Game engine and objects (using refs to persist between renders)
  const engineRef = useRef<GameEngine | null>(null);
  const playerRef = useRef<Player | null>(null);
  const levelRef = useRef<Level | null>(null);
  
  // Initialize game
  useEffect(() => {
    if (!canvasRef.current) return;
    
    // Create game engine
    engineRef.current = new GameEngine(canvasRef.current);
    
    // Create player
    playerRef.current = new Player(100, 320);
    
    // Add event listeners for touch controls (mobile)
    setupTouchControls();
    
    // Start with title screen
    setGameScreenState(GameScreenState.TITLE);
    
    return () => {
      // Cleanup
      if (engineRef.current) {
        // Stop game loop
        engineRef.current.pause();
      }
      
      // Remove touch event listeners
      cleanupTouchControls();
    };
  }, []);
  
  // Handle keyboard input
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (gameScreenState === GameScreenState.TITLE) {
        if (e.key === 'Enter' || e.key === ' ') {
          startGame();
        }
      } else if (gameScreenState === GameScreenState.LEVEL_INTRO) {
        if (e.key === 'Enter' || e.key === ' ') {
          setGameScreenState(GameScreenState.PLAYING);
          if (engineRef.current) {
            engineRef.current.start();
          }
        }
      } else if (gameScreenState === GameScreenState.LEVEL_COMPLETE) {
        if (e.key === 'Enter' || e.key === ' ') {
          loadNextLevel();
        }
      } else if (gameScreenState === GameScreenState.GAME_OVER || gameScreenState === GameScreenState.VICTORY) {
        if (e.key === 'Enter' || e.key === ' ') {
          restartGame();
        }
      }
    };
    
    window.addEventListener('keydown', handleKeyDown);
    
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [gameScreenState, currentLevel]);
  
  // Game loop
  useEffect(() => {
    if (gameScreenState !== GameScreenState.PLAYING) return;
    
    // Initialize level if not already done
    if (!levelRef.current) {
      initializeLevel(currentLevel);
    }
    
    // Add player to game engine
    if (engineRef.current && playerRef.current) {
      engineRef.current.addGameObject(playerRef.current);
    }
    
    // Game update function
    const gameUpdate = () => {
      if (!engineRef.current || !playerRef.current || !levelRef.current) return;
      
      // Get input state
      const inputState = engineRef.current.getInputState();
      
      // Update player based on input
      if (inputState.left) {
        playerRef.current.moveLeft();
      } else if (inputState.right) {
        playerRef.current.moveRight();
      } else {
        playerRef.current.stopMoving();
      }
      
      if (inputState.up) {
        playerRef.current.jump();
      }
      
      if (inputState.down) {
        playerRef.current.crouch();
      } else if (!inputState.down && !inputState.up) {
        playerRef.current.standUp();
      }
      
      if (inputState.punch) {
        playerRef.current.punch();
      }
      
      if (inputState.kick) {
        playerRef.current.kick();
      }
      
      // Check for level completion
      if (levelRef.current.isComplete()) {
        handleLevelComplete();
      }
      
      // Check for game over conditions
      const gameStateData = engineRef.current.getGameState();
      if (gameStateData.lives <= 0 || levelRef.current.getTimeRemaining() <= 0) {
        handleGameOver();
      }
      
      // Update game state in React state (for UI)
      setGameState(engineRef.current.getGameState());
    };
    
    // Set up interval for game logic updates
    const gameUpdateInterval = setInterval(gameUpdate, 16); // ~60fps
    
    return () => {
      clearInterval(gameUpdateInterval);
    };
  }, [gameScreenState, currentLevel]);
  
  // Initialize a level
  const initializeLevel = (levelNumber: number) => {
    if (!engineRef.current) return;
    
    // Create level
    const levelConfig = levelConfigs[levelNumber - 1];
    levelRef.current = new Level(levelConfig);
    
    // Add level enemies to game engine
    levelRef.current.getEnemies().forEach(enemy => {
      if (engineRef.current) {
        engineRef.current.addGameObject(enemy);
      }
    });
    
    // Add level hazards to game engine
    levelRef.current.getHazards().forEach(hazard => {
      if (engineRef.current) {
        engineRef.current.addGameObject(hazard);
      }
    });
    
    // Add boss to game engine if present
    const boss = levelRef.current.getBoss();
    if (boss && engineRef.current) {
      engineRef.current.addGameObject(boss);
    }
    
    // Reset player position
    if (playerRef.current) {
      playerRef.current.x = 100;
      playerRef.current.y = levelRef.current.getGroundY() - playerRef.current.height;
    }
  };
  
  // Start the game
  const startGame = () => {
    setCurrentLevel(1);
    setGameScreenState(GameScreenState.LEVEL_INTRO);
    
    // Reset game state
    if (engineRef.current) {
      engineRef.current.reset();
      engineRef.current.updateGameState({
        score: 0,
        lives: 3,
        level: 1,
        health: 100,
        maxHealth: 100
      });
    }
    
    // Initialize first level
    initializeLevel(1);
  };
  
  // Handle level completion
  const handleLevelComplete = () => {
    if (engineRef.current) {
      engineRef.current.pause();
    }
    
    setGameScreenState(GameScreenState.LEVEL_COMPLETE);
    
    // Clear level reference to force reinitialization
    levelRef.current = null;
  };
  
  // Load the next level
  const loadNextLevel = () => {
    const nextLevel = currentLevel + 1;
    
    if (nextLevel > levelConfigs.length) {
      // Game completed
      setGameScreenState(GameScreenState.VICTORY);
    } else {
      setCurrentLevel(nextLevel);
      
      // Update game state
      if (engineRef.current) {
        engineRef.current.updateGameState({
          level: nextLevel,
          health: 100 // Restore health between levels
        });
      }
      
      setGameScreenState(GameScreenState.LEVEL_INTRO);
    }
  };
  
  // Handle game over
  const handleGameOver = () => {
    if (engineRef.current) {
      engineRef.current.pause();
    }
    
    setGameScreenState(GameScreenState.GAME_OVER);
  };
  
  // Restart the game
  const restartGame = () => {
    setGameScreenState(GameScreenState.TITLE);
    
    // Clear level reference
    levelRef.current = null;
  };
  
  // Setup touch controls for mobile
  const setupTouchControls = () => {
    // This would be implemented with touch event handlers
    // For virtual d-pad and attack buttons
    if (!canvasRef.current) return;
    
    const handleTouchStart = (e: TouchEvent) => {
      e.preventDefault();
      const touch = e.touches[0];
      const element = document.elementFromPoint(touch.clientX, touch.clientY);
      
      if (element) {
        if (element.classList.contains('d-pad-up')) {
          if (engineRef.current) engineRef.current.inputState.up = true;
        } else if (element.classList.contains('d-pad-right')) {
          if (engineRef.current) engineRef.current.inputState.right = true;
        } else if (element.classList.contains('d-pad-down')) {
          if (engineRef.current) engineRef.current.inputState.down = true;
        } else if (element.classList.contains('d-pad-left')) {
          if (engineRef.current) engineRef.current.inputState.left = true;
        } else if (element.classList.contains('punch-button')) {
          if (engineRef.current) engineRef.current.inputState.punch = true;
        } else if (element.classList.contains('kick-button')) {
          if (engineRef.current) engineRef.current.inputState.kick = true;
        }
      }
    };
    
    const handleTouchEnd = (e: TouchEvent) => {
      e.preventDefault();
      if (engineRef.current) {
        engineRef.current.inputState.up = false;
        engineRef.current.inputState.right = false;
        engineRef.current.inputState.down = false;
        engineRef.current.inputState.left = false;
        engineRef.current.inputState.punch = false;
        engineRef.current.inputState.kick = false;
      }
    };
    
    document.addEventListener('touchstart', handleTouchStart, { passive: false });
    document.addEventListener('touchend', handleTouchEnd, { passive: false });
  };
  
  // Cleanup touch controls
  const cleanupTouchControls = () => {
    // Remove touch event listeners
    document.removeEventListener('touchstart', () => {});
    document.removeEventListener('touchend', () => {});
  };
  
  // Render different screens based on game state
  const renderGameScreen = () => {
    switch (gameScreenState) {
      case GameScreenState.TITLE:
        return (
          <div className="game-screen title-screen">
            <h1>Kung Fu</h1>
            <p>A modern remake of the classic NES game</p>
            <button onClick={startGame}>Start Game</button>
          </div>
        );
      
      case GameScreenState.LEVEL_INTRO:
        return (
          <div className="game-screen level-intro">
            <h2>Level {currentLevel}</h2>
            <p>{levelConfigs[currentLevel - 1].name}</p>
            <p>Press Enter or tap to start</p>
          </div>
        );
      
      case GameScreenState.LEVEL_COMPLETE:
        return (
          <div className="game-screen level-complete">
            <h2>Level Complete!</h2>
            <p>Score: {gameState.score}</p>
            <p>Press Enter or tap to continue</p>
          </div>
        );
      
      case GameScreenState.GAME_OVER:
        return (
          <div className="game-screen game-over">
            <h2>Game Over</h2>
            <p>Final Score: {gameState.score}</p>
            <button onClick={restartGame}>Try Again</button>
          </div>
        );
      
      case GameScreenState.VICTORY:
        return (
          <div className="game-screen victory">
            <h2>Victory!</h2>
            <p>You have rescued Sylvia and defeated Mr. X!</p>
            <p>Final Score: {gameState.score}</p>
            <button onClick={restartGame}>Play Again</button>
          </div>
        );
      
      default:
        return null;
    }
  };
  
  // Render touch controls for mobile
  const renderTouchControls = () => {
    if (gameScreenState !== GameScreenState.PLAYING) return null;
    
    return (
      <div className="touch-controls">
        <div className="d-pad">
          <button className="d-pad-up">↑</button>
          <button className="d-pad-right">→</button>
          <button className="d-pad-down">↓</button>
          <button className="d-pad-left">←</button>
        </div>
        <div className="action-buttons">
          <button className="punch-button">Punch</button>
          <button className="kick-button">Kick</button>
        </div>
      </div>
    );
  };
  
  // Manual rendering function for gameplay
  const renderGameplay = () => {
    if (canvasRef.current && gameScreenState === GameScreenState.PLAYING) {
      const ctx = canvasRef.current.getContext('2d');
      if (!ctx) return;
      
      // Clear the canvas
      ctx.clearRect(0, 0, canvasRef.current.width, canvasRef.current.height);
      
      // Draw level background
      if (levelRef.current) {
        // Draw background color based on level
        ctx.fillStyle = `hsl(${currentLevel * 60}, 50%, 30%)`;
        ctx.fillRect(0, 0, canvasRef.current.width, canvasRef.current.height);
        
        // Draw ground
        const groundY = levelRef.current.getGroundY();
        ctx.fillStyle = `hsl(${currentLevel * 60}, 40%, 20%)`;
        ctx.fillRect(0, groundY, canvasRef.current.width, canvasRef.current.height - groundY);
      }
      
      // Draw player
      if (playerRef.current) {
        ctx.fillStyle = 'blue';
        ctx.fillRect(
          playerRef.current.x, 
          playerRef.current.y, 
          playerRef.current.width, 
          playerRef.current.height
        );
        
        // Draw player state
        ctx.fillStyle = 'white';
        ctx.font = '12px Arial';
        ctx.fillText(`Player`, playerRef.current.x, playerRef.current.y - 5);
      }
      
      // Draw enemies
      if (levelRef.current) {
        const enemies = levelRef.current.getEnemies();
        enemies.forEach(enemy => {
          ctx.fillStyle = 'red';
          ctx.fillRect(enemy.x, enemy.y, enemy.width, enemy.height);
          
          // Draw enemy type
          ctx.fillStyle = 'white';
          ctx.font = '12px Arial';
          ctx.fillText(enemy.type, enemy.x, enemy.y - 5);
        });
        
        // Draw hazards
        const hazards = levelRef.current.getHazards();
        hazards.forEach(hazard => {
          ctx.fillStyle = 'orange';
          ctx.fillRect(hazard.x, hazard.y, hazard.width, hazard.height);
        });
        
        // Draw boss if present
        const boss = levelRef.current.getBoss();
        if (boss) {
          ctx.fillStyle = 'purple';
          ctx.fillRect(boss.x, boss.y, boss.width, boss.height);
          
          // Draw boss type
          ctx.fillStyle = 'white';
          ctx.font = '14px Arial';
          ctx.fillText(`BOSS: ${boss.type}`, boss.x, boss.y - 10);
        }
      }
      
      // Draw UI elements
      // Score
      ctx.fillStyle = 'white';
      ctx.font = '20px Arial';
      ctx.fillText(`Score: ${gameState.score}`, 20, 30);
      
      // Lives
      ctx.fillText(`Lives: ${gameState.lives}`, 20, 60);
      
      // Health bar
      const healthBarWidth = 200;
      const healthBarHeight = 20;
      const healthPercentage = gameState.health / gameState.maxHealth;
      
      // Health bar background
      ctx.fillStyle = 'gray';
      ctx.fillRect(
        canvasRef.current.width - healthBarWidth - 20,
        20,
        healthBarWidth,
        healthBarHeight
      );
      
      // Health bar fill
      ctx.fillStyle = healthPercentage > 0.5 ? 'green' : healthPercentage > 0.25 ? 'yellow' : 'red';
      ctx.fillRect(
        canvasRef.current.width - healthBarWidth - 20,
        20,
        healthBarWidth * healthPercentage,
        healthBarHeight
      );
      
      // Health bar border
      ctx.strokeStyle = 'black';
      ctx.lineWidth = 2;
      ctx.strokeRect(
        canvasRef.current.width - healthBarWidth - 20,
        20,
        healthBarWidth,
        healthBarHeight
      );
      
      // Level and time
      if (levelRef.current) {
        ctx.fillStyle = 'white';
        ctx.fillText(`Level ${currentLevel}: ${levelConfigs[currentLevel - 1].name}`, 20, 90);
        ctx.fillText(`Time: ${Math.max(0, Math.floor(levelRef.current.getTimeRemaining()))}`, 20, 120);
      }
      
      // Draw a blue player character
      if (playerRef.current) {
        ctx.fillStyle = 'blue';
        ctx.fillRect(100, 350, 50, 80);
        ctx.fillStyle = 'white';
        ctx.font = '16px Arial';
        ctx.fillText('Player', 100, 340);
      }
      
      // Draw some enemies
      ctx.fillStyle = 'red';
      ctx.fillRect(300, 350, 40, 70);
      ctx.fillStyle = 'white';
      ctx.fillText('Gripper', 300, 340);
      
      ctx.fillStyle = 'red';
      ctx.fillRect(500, 350, 40, 70);
      ctx.fillStyle = 'white';
      ctx.fillText('Knife Thrower', 500, 340);
      
      ctx.fillStyle = 'red';
      ctx.fillRect(700, 350, 35, 50);
      ctx.fillStyle = 'white';
      ctx.fillText('Tom Tom', 700, 340);
    }
  };
  
  // Use useEffect to set up the rendering loop
  useEffect(() => {
    if (gameScreenState === GameScreenState.PLAYING) {
      // Initial render
      renderGameplay();
      
      // Set up the rendering loop
      const renderInterval = setInterval(renderGameplay, 16); // ~60fps
      
      return () => {
        clearInterval(renderInterval);
      };
    }
  }, [gameScreenState, gameState]);
  
  return (
    <div className="game-container">
      {gameScreenState === GameScreenState.PLAYING ? (
        <>
          <canvas 
            ref={canvasRef} 
            className="game-canvas" 
            width="800" 
            height="450" 
            style={{ border: '2px solid #444', background: '#000' }}
          />
          {renderTouchControls()}
        </>
      ) : (
        renderGameScreen()
      )}
    </div>
  );
};

export default Game;
