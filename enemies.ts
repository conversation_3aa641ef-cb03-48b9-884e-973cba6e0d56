// @ts-nocheck
/**
 * Enemy class for Kung Fu game
 * Base class for all enemy types with shared functionality
 */

import { GameObject } from './engine';

export interface EnemyState {
  isAttacking: boolean;
  isHurt: boolean;
  isDying: boolean;
  health: number;
}

export class Enemy implements GameObject {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  velocityX: number;
  velocityY: number;
  type: string;
  state: string;
  direction: 'left' | 'right';
  
  // Enemy-specific properties
  protected speed: number;
  protected attackRange: number;
  protected attackDamage: number;
  protected attackCooldown: number;
  protected attackTimer: number;
  protected enemyState: EnemyState;
  protected animationFrame: number;
  protected animationTimer: number;
  protected sprites: { [key: string]: HTMLImageElement } = {};
  protected scoreValue: number;
  
  constructor(id: string, x: number, y: number, type: string) {
    this.id = id;
    this.x = x;
    this.y = y;
    this.width = 40;
    this.height = 70;
    this.velocityX = 0;
    this.velocityY = 0;
    this.type = type;
    this.state = 'idle';
    this.direction = 'left';
    
    this.speed = 2;
    this.attackRange = 50;
    this.attackDamage = 10;
    this.attackCooldown = 1000;
    this.attackTimer = 0;
    
    this.enemyState = {
      isAttacking: false,
      isHurt: false,
      isDying: false,
      health: 100
    };
    
    this.animationFrame = 0;
    this.animationTimer = 0;
    this.scoreValue = 100;
    
    // Load sprites (these would be actual sprite images in the final game)
    this.loadSprites();
  }
  
  protected loadSprites() {
    // In the actual implementation, we would load all the necessary sprites
    // For now, we'll just create placeholders
    const spriteNames = [
      'idle-right', 'idle-left',
      'walk-right', 'walk-left',
      'attack-right', 'attack-left',
      'hurt-right', 'hurt-left',
      'die-right', 'die-left'
    ];
    
    // This is a placeholder. In the actual implementation, we would load real images
    spriteNames.forEach(name => {
      this.sprites[name] = new Image();
      // this.sprites[name].src = `/assets/enemies/${this.type}/${name}.png`;
    });
  }
  
  update(deltaTime: number) {
    // Update animation timer
    this.animationTimer += deltaTime;
    if (this.animationTimer > 100) {
      this.animationTimer = 0;
      this.animationFrame = (this.animationFrame + 1) % 4; // Assuming 4 frames per animation
    }
    
    // Update attack timer
    if (this.attackTimer > 0) {
      this.attackTimer -= deltaTime;
    }
    
    // If dying, don't do anything else
    if (this.enemyState.isDying) {
      // If death animation is complete, mark for removal
      if (this.animationFrame >= 3) {
        // In a real implementation, we would signal to remove this enemy
        // For now, we'll just move it off-screen
        this.x = -1000;
      }
      return;
    }
    
    // If hurt, don't move
    if (this.enemyState.isHurt) {
      // Reset hurt state after a short time
      if (this.animationTimer > 300) {
        this.enemyState.isHurt = false;
      }
      return;
    }
    
    // Basic AI behavior - move towards player
    // In a real implementation, this would be more sophisticated
    // and would take the player's position into account
    this.velocityX = this.direction === 'left' ? -this.speed : this.speed;
    
    // Update position
    this.x += this.velocityX;
    this.y += this.velocityY;
    
    // Ground collision (placeholder - would be handled by level collision in full implementation)
    const groundY = 400; // Placeholder ground level
    if (this.y + this.height > groundY) {
      this.y = groundY - this.height;
      this.velocityY = 0;
    }
    
    // Reset attack state after animation completes
    if (this.enemyState.isAttacking) {
      // Assuming attack animations last 300ms
      if (this.animationTimer > 300) {
        this.enemyState.isAttacking = false;
      }
    }
  }
  
  render(ctx: CanvasRenderingContext2D) {
    // Determine which sprite to use based on enemy state
    let spriteName = 'idle';
    
    if (this.velocityX !== 0 && !this.enemyState.isAttacking) {
      spriteName = 'walk';
    }
    
    if (this.enemyState.isAttacking) {
      spriteName = 'attack';
    }
    
    if (this.enemyState.isHurt) {
      spriteName = 'hurt';
    }
    
    if (this.enemyState.isDying) {
      spriteName = 'die';
    }
    
    // Add direction suffix
    spriteName += `-${this.direction}`;
    
    // For development, draw a colored rectangle
    if (this.enemyState.isDying) {
      ctx.fillStyle = 'rgba(255, 0, 0, 0.5)';
    } else if (this.enemyState.isHurt) {
      ctx.fillStyle = 'orange';
    } else if (this.enemyState.isAttacking) {
      ctx.fillStyle = 'red';
    } else {
      ctx.fillStyle = 'green';
    }
    
    ctx.fillRect(this.x, this.y, this.width, this.height);
    
    // In the final implementation, we would draw the sprite instead
    // ctx.drawImage(this.sprites[spriteName], this.x, this.y, this.width, this.height);
    
    // Draw a label for the current state (for development)
    ctx.fillStyle = 'white';
    ctx.font = '12px Arial';
    ctx.fillText(`${this.type}: ${spriteName}`, this.x, this.y - 5);
    
    // Draw health bar
    const healthBarWidth = 40;
    const healthBarHeight = 5;
    const healthPercentage = this.enemyState.health / 100;
    
    // Health bar background
    ctx.fillStyle = 'gray';
    ctx.fillRect(
      this.x,
      this.y - 15,
      healthBarWidth,
      healthBarHeight
    );
    
    // Health bar fill
    ctx.fillStyle = healthPercentage > 0.5 ? 'green' : healthPercentage > 0.25 ? 'yellow' : 'red';
    ctx.fillRect(
      this.x,
      this.y - 15,
      healthBarWidth * healthPercentage,
      healthBarHeight
    );
  }
  
  attack() {
    if (!this.enemyState.isAttacking && this.attackTimer <= 0) {
      this.enemyState.isAttacking = true;
      this.attackTimer = this.attackCooldown;
      this.animationTimer = 0; // Reset animation timer for attack
      return true;
    }
    return false;
  }
  
  takeDamage(amount: number) {
    if (!this.enemyState.isDying) {
      this.enemyState.health -= amount;
      this.enemyState.isHurt = true;
      this.animationTimer = 0; // Reset animation timer for hurt animation
      
      // Check if enemy is defeated
      if (this.enemyState.health <= 0) {
        this.die();
        return this.scoreValue; // Return score value when defeated
      }
    }
    return 0;
  }
  
  die() {
    this.enemyState.isDying = true;
    this.enemyState.isHurt = false;
    this.enemyState.isAttacking = false;
    this.velocityX = 0;
    this.animationFrame = 0;
    this.animationTimer = 0; // Reset animation timer for death animation
  }
  
  getAttackBox() {
    // Return the attack hitbox based on current state
    if (this.enemyState.isAttacking) {
      if (this.direction === 'right') {
        return {
          x: this.x + this.width,
          y: this.y + this.height * 0.3,
          width: this.attackRange,
          height: this.height * 0.4
        };
      } else {
        return {
          x: this.x - this.attackRange,
          y: this.y + this.height * 0.3,
          width: this.attackRange,
          height: this.height * 0.4
        };
      }
    }
    
    // Return empty hitbox if not attacking
    return { x: 0, y: 0, width: 0, height: 0 };
  }
  
  isAttacking() {
    return this.enemyState.isAttacking;
  }
  
  getScoreValue() {
    return this.scoreValue;
  }
}

// Specific enemy types that extend the base Enemy class

export class Gripper extends Enemy {
  constructor(id: string, x: number, y: number) {
    super(id, x, y, 'gripper');
    this.width = 40;
    this.height = 70;
    this.speed = 1.5;
    this.attackRange = 30;
    this.attackDamage = 5;
    this.attackCooldown = 1500;
    this.enemyState.health = 30;
    this.scoreValue = 100;
  }
  
  // Override update method to add gripper-specific behavior
  update(deltaTime: number) {
    super.update(deltaTime);
    
    // Grippers try to grab the player and drain energy
    // This would be implemented with player proximity detection
  }
}

export class KnifeThrower extends Enemy {
  private throwCooldown: number = 2000;
  private throwTimer: number = 0;
  
  constructor(id: string, x: number, y: number) {
    super(id, x, y, 'knifeThrower');
    this.width = 40;
    this.height = 70;
    this.speed = 1;
    this.attackRange = 200; // Longer range for throwing knives
    this.attackDamage = 15;
    this.attackCooldown = 2000;
    this.enemyState.health = 20;
    this.scoreValue = 150;
  }
  
  // Override update method to add knife thrower-specific behavior
  update(deltaTime: number) {
    super.update(deltaTime);
    
    // Update throw timer
    if (this.throwTimer > 0) {
      this.throwTimer -= deltaTime;
    }
    
    // Knife throwers stay at a distance and throw knives
    // This would create knife projectiles in the game
    if (this.throwTimer <= 0 && !this.enemyState.isHurt && !this.enemyState.isDying) {
      this.throwKnife();
      this.throwTimer = this.throwCooldown;
    }
  }
  
  throwKnife() {
    // In a real implementation, this would create a knife projectile
    this.enemyState.isAttacking = true;
    this.animationTimer = 0;
  }
}

export class TomTom extends Enemy {
  private canSomersault: boolean = true;
  private somersaultCooldown: number = 3000;
  private somersaultTimer: number = 0;
  
  constructor(id: string, x: number, y: number) {
    super(id, x, y, 'tomTom');
    this.width = 35;
    this.height = 50; // Shorter than other enemies
    this.speed = 3; // Faster than other enemies
    this.attackRange = 40;
    this.attackDamage = 10;
    this.attackCooldown = 1000;
    this.enemyState.health = 15;
    this.scoreValue = 200;
  }
  
  // Override update method to add Tom Tom-specific behavior
  update(deltaTime: number) {
    super.update(deltaTime);
    
    // Update somersault timer
    if (this.somersaultTimer > 0) {
      this.somersaultTimer -= deltaTime;
    } else {
      this.canSomersault = true;
    }
    
    // Tom Toms can somersault to attack the player's head when crouching
    // This would be implemented with player state detection
  }
  
  somersault() {
    if (this.canSomersault && !this.enemyState.isHurt && !this.enemyState.isDying) {
      this.enemyState.isAttacking = true;
      this.animationTimer = 0;
      this.canSomersault = false;
      this.somersaultTimer = this.somersaultCooldown;
      
      // In a real implementation, this would trigger a jumping attack animation
      // and move the enemy in an arc
    }
  }
}
