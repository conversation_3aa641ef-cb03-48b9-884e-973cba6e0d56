/* Game styles */
.game-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100vh;
  background-color: #111;
  color: white;
  font-family: 'Press Start 2P', system-ui, sans-serif;
}

.game-canvas {
  border: 4px solid #444;
  background-color: #000;
  max-width: 100%;
  max-height: 80vh;
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
}

/* Game screens */
.game-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  text-align: center;
  padding: 2rem;
}

.title-screen {
  background: linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), 
              url('/assets/backgrounds/title-bg.jpg') center/cover;
}

.title-screen h1 {
  font-size: 4rem;
  margin-bottom: 1rem;
  color: #f00;
  text-shadow: 0 0 10px rgba(255, 0, 0, 0.7);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.level-intro, .level-complete, .game-over, .victory {
  background-color: rgba(0, 0, 0, 0.8);
}

.level-intro h2, .level-complete h2, .game-over h2, .victory h2 {
  font-size: 2.5rem;
  margin-bottom: 1.5rem;
  color: #f00;
}

.victory h2 {
  color: #ff0;
  animation: rainbow 3s infinite;
}

@keyframes rainbow {
  0% { color: #ff0000; }
  16% { color: #ff8000; }
  33% { color: #ffff00; }
  50% { color: #00ff00; }
  66% { color: #00ffff; }
  83% { color: #0000ff; }
  100% { color: #ff0000; }
}

/* Buttons */
button {
  background-color: #f00;
  color: white;
  border: none;
  padding: 1rem 2rem;
  font-size: 1.2rem;
  margin-top: 2rem;
  cursor: pointer;
  font-family: 'Press Start 2P', system-ui, sans-serif;
  transition: all 0.2s;
  border-radius: 4px;
  box-shadow: 0 4px 0 #900;
}

button:hover {
  background-color: #d00;
  transform: translateY(2px);
  box-shadow: 0 2px 0 #900;
}

button:active {
  transform: translateY(4px);
  box-shadow: none;
}

/* Touch controls */
.touch-controls {
  position: absolute;
  bottom: 20px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
  pointer-events: none;
}

.d-pad {
  position: relative;
  width: 150px;
  height: 150px;
  pointer-events: auto;
}

.d-pad button {
  position: absolute;
  width: 50px;
  height: 50px;
  background-color: rgba(255, 255, 255, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  margin: 0;
  padding: 0;
  box-shadow: none;
}

.d-pad-up {
  top: 0;
  left: 50px;
}

.d-pad-right {
  top: 50px;
  left: 100px;
}

.d-pad-down {
  top: 100px;
  left: 50px;
}

.d-pad-left {
  top: 50px;
  left: 0;
}

.action-buttons {
  display: flex;
  gap: 20px;
  pointer-events: auto;
}

.action-buttons button {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  font-size: 0.8rem;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.punch-button {
  background-color: #f00;
}

.kick-button {
  background-color: #00f;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .title-screen h1 {
    font-size: 2.5rem;
  }
  
  .level-intro h2, .level-complete h2, .game-over h2, .victory h2 {
    font-size: 1.8rem;
  }
  
  button {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
  }
  
  .d-pad {
    width: 120px;
    height: 120px;
  }
  
  .d-pad button {
    width: 40px;
    height: 40px;
  }
  
  .d-pad-up {
    left: 40px;
  }
  
  .d-pad-right {
    top: 40px;
    left: 80px;
  }
  
  .d-pad-down {
    top: 80px;
    left: 40px;
  }
  
  .action-buttons button {
    width: 60px;
    height: 60px;
  }
}
