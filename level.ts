// @ts-nocheck
/**
 * Level class for Kung Fu game
 * Manages level structure, backgrounds, and enemy spawning
 */

import { GameObject } from './engine';
import { <PERSON>, Grip<PERSON>, KnifeThrower, <PERSON>Tom } from './enemies';

export interface LevelConfig {
  id: number;
  name: string;
  background: string;
  width: number;
  groundY: number;
  enemySpawnPoints: {
    type: string;
    x: number;
    y: number;
    direction: 'left' | 'right';
  }[];
  hazards: {
    type: string;
    x: number;
    y: number;
    width: number;
    height: number;
  }[];
  boss: {
    type: string;
    x: number;
    y: number;
  };
  timeLimit: number;
}

export class Level {
  private config: LevelConfig;
  private background: HTMLImageElement;
  private enemies: Enemy[] = [];
  private hazards: GameObject[] = [];
  private boss: Enemy | null = null;
  private timeRemaining: number;
  private levelComplete: boolean = false;
  
  constructor(config: LevelConfig) {
    this.config = config;
    this.timeRemaining = config.timeLimit;
    
    // Load background image
    this.background = new Image();
    // this.background.src = `/assets/backgrounds/level${config.id}.png`;
    
    // Initialize enemies based on spawn points
    this.initializeEnemies();
    
    // Initialize hazards
    this.initializeHazards();
    
    // Initialize boss
    this.initializeBoss();
  }
  
  private initializeEnemies() {
    this.config.enemySpawnPoints.forEach((spawnPoint, index) => {
      let enemy: Enemy;
      const id = `enemy-${this.config.id}-${index}`;
      
      switch (spawnPoint.type) {
        case 'gripper':
          enemy = new Gripper(id, spawnPoint.x, spawnPoint.y);
          break;
        case 'knifeThrower':
          enemy = new KnifeThrower(id, spawnPoint.x, spawnPoint.y);
          break;
        case 'tomTom':
          enemy = new TomTom(id, spawnPoint.x, spawnPoint.y);
          break;
        default:
          enemy = new Enemy(id, spawnPoint.x, spawnPoint.y, 'generic');
      }
      
      enemy.direction = spawnPoint.direction;
      this.enemies.push(enemy);
    });
  }
  
  private initializeHazards() {
    // Create hazard game objects based on config
    this.config.hazards.forEach((hazardConfig, index) => {
      const hazard: GameObject = {
        id: `hazard-${this.config.id}-${index}`,
        x: hazardConfig.x,
        y: hazardConfig.y,
        width: hazardConfig.width,
        height: hazardConfig.height,
        velocityX: 0,
        velocityY: 0,
        type: hazardConfig.type,
        state: 'active',
        direction: 'left',
        
        update: (deltaTime: number) => {
          // Hazard-specific update logic
          // For example, falling objects would have gravity
          if (hazardConfig.type === 'fallingObject') {
            hazard.velocityY += 0.2;
            hazard.y += hazard.velocityY;
            
            // Reset if it hits the ground
            if (hazard.y > this.config.groundY) {
              hazard.y = hazardConfig.y; // Reset to original position
              hazard.velocityY = 0;
            }
          }
        },
        
        render: (ctx: CanvasRenderingContext2D) => {
          // For development, draw a colored rectangle
          switch (hazardConfig.type) {
            case 'fallingObject':
              ctx.fillStyle = 'brown';
              break;
            case 'snake':
              ctx.fillStyle = 'purple';
              break;
            case 'moth':
              ctx.fillStyle = 'pink';
              break;
            case 'dragon':
              ctx.fillStyle = 'orange';
              break;
            case 'confettiBall':
              ctx.fillStyle = 'yellow';
              break;
            default:
              ctx.fillStyle = 'gray';
          }
          
          ctx.fillRect(hazard.x, hazard.y, hazard.width, hazard.height);
          
          // Draw a label for the hazard type (for development)
          ctx.fillStyle = 'white';
          ctx.font = '10px Arial';
          ctx.fillText(hazardConfig.type, hazard.x, hazard.y - 5);
        }
      };
      
      this.hazards.push(hazard);
    });
  }
  
  private initializeBoss() {
    if (this.config.boss) {
      const bossConfig = this.config.boss;
      const bossId = `boss-${this.config.id}`;
      
      // In a full implementation, we would have specific boss classes
      // For now, we'll use a generic enemy with boss properties
      this.boss = new Enemy(bossId, bossConfig.x, bossConfig.y, bossConfig.type);
      this.boss.width = 80; // Bosses are larger
      this.boss.height = 120;
      
      // Customize boss properties based on type
      switch (bossConfig.type) {
        case 'stickFighter':
          // First boss - Stick Fighter
          this.boss.direction = 'left';
          break;
        case 'boomerangFighter':
          // Second boss - Boomerang Fighter
          this.boss.direction = 'left';
          break;
        case 'giant':
          // Third boss - Giant
          this.boss.width = 100;
          this.boss.height = 150;
          this.boss.direction = 'left';
          break;
        case 'blackMagician':
          // Fourth boss - Black Magician
          this.boss.direction = 'left';
          break;
        case 'mrX':
          // Final boss - Mr. X
          this.boss.width = 90;
          this.boss.height = 130;
          this.boss.direction = 'left';
          break;
      }
    }
  }
  
  update(deltaTime: number) {
    // Update time remaining
    this.timeRemaining -= deltaTime / 1000; // Convert ms to seconds
    
    // Update enemies
    this.enemies.forEach(enemy => enemy.update(deltaTime));
    
    // Update hazards
    this.hazards.forEach(hazard => hazard.update(deltaTime));
    
    // Update boss if present
    if (this.boss) {
      this.boss.update(deltaTime);
    }
    
    // Check if level is complete (boss defeated)
    if (this.boss && this.boss.x < -500) { // Assuming boss is moved off-screen when defeated
      this.levelComplete = true;
    }
  }
  
  render(ctx: CanvasRenderingContext2D, cameraX: number) {
    // Draw background
    // In a real implementation, we would draw the background image
    // For now, draw a colored rectangle
    ctx.fillStyle = `hsl(${this.config.id * 60}, 50%, 30%)`;
    ctx.fillRect(0, 0, ctx.canvas.width, ctx.canvas.height);
    
    // Draw ground
    ctx.fillStyle = `hsl(${this.config.id * 60}, 40%, 20%)`;
    ctx.fillRect(0, this.config.groundY, ctx.canvas.width, ctx.canvas.height - this.config.groundY);
    
    // Draw level name and time
    ctx.fillStyle = 'white';
    ctx.font = '20px Arial';
    ctx.fillText(`Level ${this.config.id}: ${this.config.name}`, 20, 100);
    ctx.fillText(`Time: ${Math.max(0, Math.floor(this.timeRemaining))}`, 20, 130);
    
    // Draw enemies
    this.enemies.forEach(enemy => enemy.render(ctx));
    
    // Draw hazards
    this.hazards.forEach(hazard => hazard.render(ctx));
    
    // Draw boss if present
    if (this.boss) {
      this.boss.render(ctx);
    }
  }
  
  getEnemies(): Enemy[] {
    return this.enemies;
  }
  
  getHazards(): GameObject[] {
    return this.hazards;
  }
  
  getBoss(): Enemy | null {
    return this.boss;
  }
  
  isComplete(): boolean {
    return this.levelComplete;
  }
  
  getTimeRemaining(): number {
    return this.timeRemaining;
  }
  
  getGroundY(): number {
    return this.config.groundY;
  }
  
  getLevelWidth(): number {
    return this.config.width;
  }
  
  // Remove an enemy from the level (when defeated)
  removeEnemy(id: string) {
    this.enemies = this.enemies.filter(enemy => enemy.id !== id);
  }
}

// Level configurations for the five floors of the Devil's Temple
export const levelConfigs: LevelConfig[] = [
  {
    id: 1,
    name: 'Floor 1',
    background: 'level1.png',
    width: 2000,
    groundY: 400,
    enemySpawnPoints: [
      { type: 'gripper', x: 300, y: 330, direction: 'left' },
      { type: 'gripper', x: 500, y: 330, direction: 'left' },
      { type: 'tomTom', x: 700, y: 350, direction: 'left' },
      { type: 'gripper', x: 900, y: 330, direction: 'left' },
      { type: 'tomTom', x: 1100, y: 350, direction: 'left' },
      { type: 'gripper', x: 1300, y: 330, direction: 'left' },
      { type: 'knifeThrower', x: 1500, y: 330, direction: 'left' }
    ],
    hazards: [],
    boss: {
      type: 'stickFighter',
      x: 1800,
      y: 280
    },
    timeLimit: 120
  },
  {
    id: 2,
    name: 'Floor 2',
    background: 'level2.png',
    width: 2200,
    groundY: 400,
    enemySpawnPoints: [
      { type: 'gripper', x: 300, y: 330, direction: 'left' },
      { type: 'tomTom', x: 500, y: 350, direction: 'left' },
      { type: 'knifeThrower', x: 700, y: 330, direction: 'left' },
      { type: 'gripper', x: 900, y: 330, direction: 'left' },
      { type: 'tomTom', x: 1100, y: 350, direction: 'left' },
      { type: 'knifeThrower', x: 1300, y: 330, direction: 'left' },
      { type: 'gripper', x: 1500, y: 330, direction: 'left' },
      { type: 'tomTom', x: 1700, y: 350, direction: 'left' }
    ],
    hazards: [
      { type: 'fallingObject', x: 400, y: 0, width: 30, height: 30 },
      { type: 'fallingObject', x: 800, y: 50, width: 30, height: 30 },
      { type: 'fallingObject', x: 1200, y: 20, width: 30, height: 30 },
      { type: 'snake', x: 600, y: 370, width: 40, height: 20 },
      { type: 'snake', x: 1000, y: 370, width: 40, height: 20 }
    ],
    boss: {
      type: 'boomerangFighter',
      x: 2000,
      y: 280
    },
    timeLimit: 120
  },
  {
    id: 3,
    name: 'Floor 3',
    background: 'level3.png',
    width: 2400,
    groundY: 400,
    enemySpawnPoints: [
      { type: 'gripper', x: 300, y: 330, direction: 'left' },
      { type: 'tomTom', x: 450, y: 350, direction: 'left' },
      { type: 'knifeThrower', x: 600, y: 330, direction: 'left' },
      { type: 'gripper', x: 750, y: 330, direction: 'left' },
      { type: 'tomTom', x: 900, y: 350, direction: 'left' },
      { type: 'knifeThrower', x: 1050, y: 330, direction: 'left' },
      { type: 'gripper', x: 1200, y: 330, direction: 'left' },
      { type: 'tomTom', x: 1350, y: 350, direction: 'left' },
      { type: 'knifeThrower', x: 1500, y: 330, direction: 'left' },
      { type: 'gripper', x: 1650, y: 330, direction: 'left' },
      { type: 'tomTom', x: 1800, y: 350, direction: 'left' }
    ],
    hazards: [
      { type: 'moth', x: 500, y: 150, width: 30, height: 30 },
      { type: 'moth', x: 1000, y: 100, width: 30, height: 30 },
      { type: 'moth', x: 1500, y: 120, width: 30, height: 30 },
      { type: 'snake', x: 700, y: 370, width: 40, height: 20 },
      { type: 'snake', x: 1200, y: 370, width: 40, height: 20 },
      { type: 'snake', x: 1700, y: 370, width: 40, height: 20 }
    ],
    boss: {
      type: 'giant',
      x: 2200,
      y: 250
    },
    timeLimit: 150
  },
  {
    id: 4,
    name: 'Floor 4',
    background: 'level4.png',
    width: 2600,
    groundY: 400,
    enemySpawnPoints: [
      { type: 'gripper', x: 300, y: 330, direction: 'left' },
      { type: 'tomTom', x: 450, y: 350, direction: 'left' },
      { type: 'knifeThrower', x: 600, y: 330, direction: 'left' },
      { type: 'gripper', x: 750, y: 330, direction: 'left' },
      { type: 'tomTom', x: 900, y: 350, direction: 'left' },
      { type: 'knifeThrower', x: 1050, y: 330, direction: 'left' },
      { type: 'gripper', x: 1200, y: 330, direction: 'left' },
      { type: 'tomTom', x: 1350, y: 350, direction: 'left' },
      { type: 'knifeThrower', x: 1500, y: 330, direction: 'left' },
      { type: 'gripper', x: 1650, y: 330, direction: 'left' },
      { type: 'tomTom', x: 1800, y: 350, direction: 'left' },
      { type: 'knifeThrower', x: 1950, y: 330, direction: 'left' },
      { type: 'gripper', x: 2100, y: 330, direction: 'left' }
    ],
    hazards: [
      { type: 'dragon', x: 500, y: 200, width: 60, height: 40 },
      { type: 'dragon', x: 1000, y: 200, width: 60, height: 40 },
      { type: 'dragon', x: 1500, y: 200, width: 60, height: 40 },
      { type: 'dragon', x: 2000, y: 200, width: 60, height: 40 },
      { type: 'fallingObject', x: 700, y: 0, width: 30, height: 30 },
      { type: 'fallingObject', x: 1200, y: 50, width: 30, height: 30 },
      { type: 'fallingObject', x: 1700, y: 20, width: 30, height: 30 },
      { type: 'moth', x: 800, y: 150, width: 30, height: 30 },
      { type: 'moth', x: 1300, y: 100, width: 30, height: 30 },
      { type: 'moth', x: 1800, y: 120, width: 30, height: 30 }
    ],
    boss: {
      type: 'blackMagician',
      x: 2400,
      y: 280
    },
    timeLimit: 180
  },
  {
    id: 5,
    name: 'Floor 5',
    background: 'level5.png',
    width: 3000,
    groundY: 400,
    enemySpawnPoints: [
      { type: 'gripper', x: 300, y: 330, direction: 'left' },
      { type: 'tomTom', x: 400, y: 350, direction: 'left' },
      { type: 'knifeThrower', x: 500, y: 330, direction: 'left' },
      { type: 'gripper', x: 600, y: 330, direction: 'left' },
      { type: 'tomTom', x: 700, y: 350, direction: 'left' },
      { type: 'knifeThrower', x: 800, y: 330, direction: 'left' },
      { type: 'gripper', x: 900, y: 330, direction: 'left' },
      { type: 'tomTom', x: 1000, y: 350, direction: 'left' },
      { type: 'knifeThrower', x: 1100, y: 330, direction: 'left' },
      { type: 'gripper', x: 1200, y: 330, direction: 'left' },
      { type: 'tomTom', x: 1300, y: 350, direction: 'left' },
      { type: 'knifeThrower', x: 1400, y: 330, direction: 'left' },
      { type: 'gripper', x: 1500, y: 330, direction: 'left' },
      { type: 'tomTom', x: 1600, y: 350, direction: 'left' },
      { type: 'knifeThrower', x: 1700, y: 330, direction: 'left' },
      { type: 'gripper', x: 1800, y: 330, direction: 'left' },
      { type: 'tomTom', x: 1900, y: 350, direction: 'left' },
      { type: 'knifeThrower', x: 2000, y: 330, direction: 'left' },
      { type: 'gripper', x: 2100, y: 330, direction: 'left' },
      { type: 'tomTom', x: 2200, y: 350, direction: 'left' },
      { type: 'knifeThrower', x: 2300, y: 330, direction: 'left' },
      { type: 'gripper', x: 2400, y: 330, direction: 'left' },
      { type: 'tomTom', x: 2500, y: 350, direction: 'left' }
    ],
    hazards: [
      { type: 'dragon', x: 500, y: 200, width: 60, height: 40 },
      { type: 'dragon', x: 1000, y: 200, width: 60, height: 40 },
      { type: 'dragon', x: 1500, y: 200, width: 60, height: 40 },
      { type: 'dragon', x: 2000, y: 200, width: 60, height: 40 },
      { type: 'confettiBall', x: 700, y: 150, width: 40, height: 40 },
      { type: 'confettiBall', x: 1200, y: 150, width: 40, height: 40 },
      { type: 'confettiBall', x: 1700, y: 150, width: 40, height: 40 },
      { type: 'confettiBall', x: 2200, y: 150, width: 40, height: 40 },
      { type: 'fallingObject', x: 600, y: 0, width: 30, height: 30 },
      { type: 'fallingObject', x: 900, y: 50, width: 30, height: 30 },
      { type: 'fallingObject', x: 1200, y: 20, width: 30, height: 30 },
      { type: 'fallingObject', x: 1500, y: 0, width: 30, height: 30 },
      { type: 'fallingObject', x: 1800, y: 50, width: 30, height: 30 },
      { type: 'fallingObject', x: 2100, y: 20, width: 30, height: 30 },
      { type: 'moth', x: 800, y: 150, width: 30, height: 30 },
      { type: 'moth', x: 1100, y: 100, width: 30, height: 30 },
      { type: 'moth', x: 1400, y: 120, width: 30, height: 30 },
      { type: 'moth', x: 1700, y: 150, width: 30, height: 30 },
      { type: 'moth', x: 2000, y: 100, width: 30, height: 30 },
      { type: 'moth', x: 2300, y: 120, width: 30, height: 30 }
    ],
    boss: {
      type: 'mrX',
      x: 2800,
      y: 280
    },
    timeLimit: 240
  }
];
