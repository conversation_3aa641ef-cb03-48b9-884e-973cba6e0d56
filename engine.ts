// @ts-nocheck
/**
 * Game Engine for Kung Fu
 * Handles the core game loop, rendering, and state management
 */

export interface GameState {
  running: boolean;
  score: number;
  lives: number;
  level: number;
  health: number;
  maxHealth: number;
}

export interface GameObject {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  velocityX: number;
  velocityY: number;
  type: string;
  state: string;
  direction: 'left' | 'right';
  update: (deltaTime: number) => void;
  render: (ctx: CanvasRenderingContext2D) => void;
}

export class GameEngine {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;
  private lastFrameTime: number = 0;
  private gameObjects: GameObject[] = [];
  private gameState: GameState = {
    running: false,
    score: 0,
    lives: 3,
    level: 1,
    health: 100,
    maxHealth: 100
  };
  
  private inputState = {
    left: false,
    right: false,
    up: false,
    down: false,
    punch: false,
    kick: false
  };

  constructor(canvas: HTMLCanvasElement) {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d') as CanvasRenderingContext2D;
    
    // Set up event listeners for keyboard input
    window.addEventListener('keydown', this.handleKeyDown.bind(this));
    window.addEventListener('keyup', this.handleKeyUp.bind(this));
    
    // Set up event listeners for touch input (mobile)
    this.setupTouchControls();
    
    // Set up window resize handler
    window.addEventListener('resize', this.handleResize.bind(this));
    this.handleResize();
  }

  private handleResize() {
    // Make the canvas responsive
    const containerWidth = this.canvas.parentElement?.clientWidth || window.innerWidth;
    const containerHeight = this.canvas.parentElement?.clientHeight || window.innerHeight;
    
    // Set canvas size while maintaining aspect ratio
    const aspectRatio = 16 / 9;
    let width = containerWidth;
    let height = width / aspectRatio;
    
    if (height > containerHeight) {
      height = containerHeight;
      width = height * aspectRatio;
    }
    
    this.canvas.width = width;
    this.canvas.height = height;
  }

  private handleKeyDown(event: KeyboardEvent) {
    switch (event.key) {
      case 'ArrowLeft':
        this.inputState.left = true;
        break;
      case 'ArrowRight':
        this.inputState.right = true;
        break;
      case 'ArrowUp':
        this.inputState.up = true;
        break;
      case 'ArrowDown':
        this.inputState.down = true;
        break;
      case 'z':
      case 'Z':
        this.inputState.punch = true;
        break;
      case 'x':
      case 'X':
        this.inputState.kick = true;
        break;
    }
  }

  private handleKeyUp(event: KeyboardEvent) {
    switch (event.key) {
      case 'ArrowLeft':
        this.inputState.left = false;
        break;
      case 'ArrowRight':
        this.inputState.right = false;
        break;
      case 'ArrowUp':
        this.inputState.up = false;
        break;
      case 'ArrowDown':
        this.inputState.down = false;
        break;
      case 'z':
      case 'Z':
        this.inputState.punch = false;
        break;
      case 'x':
      case 'X':
        this.inputState.kick = false;
        break;
    }
  }

  private setupTouchControls() {
    // This will be implemented with virtual buttons for mobile devices
    // For now, we'll just set up the basic structure
    const createTouchHandler = (input: keyof typeof this.inputState, value: boolean) => {
      return (event: Event) => {
        event.preventDefault();
        this.inputState[input] = value;
      };
    };
    
    // These will be connected to UI elements in the React component
  }

  public start() {
    this.gameState.running = true;
    this.lastFrameTime = performance.now();
    requestAnimationFrame(this.gameLoop.bind(this));
  }

  public pause() {
    this.gameState.running = false;
  }

  public resume() {
    if (!this.gameState.running) {
      this.gameState.running = true;
      this.lastFrameTime = performance.now();
      requestAnimationFrame(this.gameLoop.bind(this));
    }
  }

  public reset() {
    this.gameState = {
      running: false,
      score: 0,
      lives: 3,
      level: 1,
      health: 100,
      maxHealth: 100
    };
    this.gameObjects = [];
  }

  public addGameObject(gameObject: GameObject) {
    this.gameObjects.push(gameObject);
  }

  public removeGameObject(id: string) {
    this.gameObjects = this.gameObjects.filter(obj => obj.id !== id);
  }

  private gameLoop(timestamp: number) {
    if (!this.gameState.running) return;
    
    // Calculate delta time (time since last frame)
    const deltaTime = timestamp - this.lastFrameTime;
    this.lastFrameTime = timestamp;
    
    // Clear the canvas
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    
    // Update all game objects
    this.gameObjects.forEach(obj => obj.update(deltaTime));
    
    // Check for collisions
    this.checkCollisions();
    
    // Render all game objects
    this.gameObjects.forEach(obj => obj.render(this.ctx));
    
    // Render UI (score, lives, health)
    this.renderUI();
    
    // Continue the game loop
    requestAnimationFrame(this.gameLoop.bind(this));
  }

  private checkCollisions() {
    // Simple collision detection between game objects
    for (let i = 0; i < this.gameObjects.length; i++) {
      const obj1 = this.gameObjects[i];
      
      for (let j = i + 1; j < this.gameObjects.length; j++) {
        const obj2 = this.gameObjects[j];
        
        // Check for collision
        if (this.isColliding(obj1, obj2)) {
          // Handle collision based on object types
          this.handleCollision(obj1, obj2);
        }
      }
    }
  }

  private isColliding(obj1: GameObject, obj2: GameObject): boolean {
    return (
      obj1.x < obj2.x + obj2.width &&
      obj1.x + obj1.width > obj2.x &&
      obj1.y < obj2.y + obj2.height &&
      obj1.y + obj1.height > obj2.y
    );
  }

  private handleCollision(obj1: GameObject, obj2: GameObject) {
    // Handle different collision types based on game objects
    // For example, player attacking enemy, enemy attacking player, etc.
    // This will be expanded as we implement more game mechanics
  }

  private renderUI() {
    // Render score
    this.ctx.fillStyle = 'white';
    this.ctx.font = '20px Arial';
    this.ctx.fillText(`Score: ${this.gameState.score}`, 20, 30);
    
    // Render lives
    this.ctx.fillText(`Lives: ${this.gameState.lives}`, 20, 60);
    
    // Render health bar
    const healthBarWidth = 200;
    const healthBarHeight = 20;
    const healthPercentage = this.gameState.health / this.gameState.maxHealth;
    
    // Health bar background
    this.ctx.fillStyle = 'gray';
    this.ctx.fillRect(
      this.canvas.width - healthBarWidth - 20,
      20,
      healthBarWidth,
      healthBarHeight
    );
    
    // Health bar fill
    this.ctx.fillStyle = healthPercentage > 0.5 ? 'green' : healthPercentage > 0.25 ? 'yellow' : 'red';
    this.ctx.fillRect(
      this.canvas.width - healthBarWidth - 20,
      20,
      healthBarWidth * healthPercentage,
      healthBarHeight
    );
    
    // Health bar border
    this.ctx.strokeStyle = 'black';
    this.ctx.lineWidth = 2;
    this.ctx.strokeRect(
      this.canvas.width - healthBarWidth - 20,
      20,
      healthBarWidth,
      healthBarHeight
    );
  }

  public getInputState() {
    return { ...this.inputState };
  }

  public getGameState() {
    return { ...this.gameState };
  }

  public updateGameState(newState: Partial<GameState>) {
    this.gameState = { ...this.gameState, ...newState };
  }
}
