// @ts-nocheck
/**
 * Player class for Kung Fu game
 * Represents the main character (<PERSON>) with all animations and actions
 */

import { GameObject } from './engine';

export interface PlayerState {
  isJumping: boolean;
  isCrouching: boolean;
  isPunching: boolean;
  isKicking: boolean;
  isHurt: boolean;
  isInvulnerable: boolean;
  invulnerabilityTimer: number;
}

export class Player implements GameObject {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  velocityX: number;
  velocityY: number;
  type: string;
  state: string;
  direction: 'left' | 'right';
  
  // Player-specific properties
  private speed: number;
  private jumpForce: number;
  private gravity: number;
  private playerState: PlayerState;
  private animationFrame: number;
  private animationTimer: number;
  private sprites: { [key: string]: HTMLImageElement } = {};
  
  constructor(x: number, y: number) {
    this.id = 'player';
    this.x = x;
    this.y = y;
    this.width = 50;
    this.height = 80;
    this.velocityX = 0;
    this.velocityY = 0;
    this.type = 'player';
    this.state = 'idle';
    this.direction = 'right';
    
    this.speed = 5;
    this.jumpForce = 15;
    this.gravity = 0.5;
    
    this.playerState = {
      isJumping: false,
      isCrouching: false,
      isPunching: false,
      isKicking: false,
      isHurt: false,
      isInvulnerable: false,
      invulnerabilityTimer: 0
    };
    
    this.animationFrame = 0;
    this.animationTimer = 0;
    
    // Load sprites (these would be actual sprite images in the final game)
    this.loadSprites();
  }
  
  private loadSprites() {
    // In the actual implementation, we would load all the necessary sprites
    // For now, we'll just create placeholders
    const spriteNames = [
      'idle-right', 'idle-left',
      'walk-right', 'walk-left',
      'jump-right', 'jump-left',
      'crouch-right', 'crouch-left',
      'punch-right', 'punch-left',
      'kick-right', 'kick-left',
      'hurt-right', 'hurt-left'
    ];
    
    // This is a placeholder. In the actual implementation, we would load real images
    spriteNames.forEach(name => {
      this.sprites[name] = new Image();
      // this.sprites[name].src = `/assets/player/${name}.png`;
    });
  }
  
  update(deltaTime: number) {
    // Update animation timer
    this.animationTimer += deltaTime;
    if (this.animationTimer > 100) {
      this.animationTimer = 0;
      this.animationFrame = (this.animationFrame + 1) % 4; // Assuming 4 frames per animation
    }
    
    // Apply gravity
    if (this.playerState.isJumping) {
      this.velocityY += this.gravity;
    }
    
    // Update position
    this.x += this.velocityX;
    this.y += this.velocityY;
    
    // Ground collision (placeholder - would be handled by level collision in full implementation)
    const groundY = 400; // Placeholder ground level
    if (this.y + this.height > groundY) {
      this.y = groundY - this.height;
      this.velocityY = 0;
      this.playerState.isJumping = false;
    }
    
    // Update invulnerability timer
    if (this.playerState.isInvulnerable) {
      this.playerState.invulnerabilityTimer -= deltaTime;
      if (this.playerState.invulnerabilityTimer <= 0) {
        this.playerState.isInvulnerable = false;
      }
    }
    
    // Reset attack states after animation completes
    if (this.playerState.isPunching || this.playerState.isKicking) {
      // Assuming attack animations last 300ms
      if (this.animationTimer > 300) {
        this.playerState.isPunching = false;
        this.playerState.isKicking = false;
      }
    }
  }
  
  render(ctx: CanvasRenderingContext2D) {
    // Determine which sprite to use based on player state
    let spriteName = 'idle';
    
    if (this.velocityX !== 0) {
      spriteName = 'walk';
    }
    
    if (this.playerState.isJumping) {
      spriteName = 'jump';
    }
    
    if (this.playerState.isCrouching) {
      spriteName = 'crouch';
    }
    
    if (this.playerState.isPunching) {
      spriteName = 'punch';
    }
    
    if (this.playerState.isKicking) {
      spriteName = 'kick';
    }
    
    if (this.playerState.isHurt) {
      spriteName = 'hurt';
    }
    
    // Add direction suffix
    spriteName += `-${this.direction}`;
    
    // For development, draw a colored rectangle
    if (this.playerState.isInvulnerable) {
      // Flash when invulnerable
      if (Math.floor(this.playerState.invulnerabilityTimer / 100) % 2 === 0) {
        ctx.fillStyle = 'rgba(255, 255, 255, 0.5)';
      } else {
        ctx.fillStyle = 'rgba(255, 0, 0, 0.5)';
      }
    } else {
      ctx.fillStyle = 'blue';
    }
    
    ctx.fillRect(this.x, this.y, this.width, this.height);
    
    // In the final implementation, we would draw the sprite instead
    // ctx.drawImage(this.sprites[spriteName], this.x, this.y, this.width, this.height);
    
    // Draw a label for the current state (for development)
    ctx.fillStyle = 'white';
    ctx.font = '12px Arial';
    ctx.fillText(spriteName, this.x, this.y - 5);
  }
  
  // Player control methods
  moveLeft() {
    if (!this.playerState.isPunching && !this.playerState.isKicking) {
      this.velocityX = -this.speed;
      this.direction = 'left';
    }
  }
  
  moveRight() {
    if (!this.playerState.isPunching && !this.playerState.isKicking) {
      this.velocityX = this.speed;
      this.direction = 'right';
    }
  }
  
  stopMoving() {
    this.velocityX = 0;
  }
  
  jump() {
    if (!this.playerState.isJumping && !this.playerState.isCrouching) {
      this.playerState.isJumping = true;
      this.velocityY = -this.jumpForce;
    }
  }
  
  crouch() {
    if (!this.playerState.isJumping) {
      this.playerState.isCrouching = true;
    }
  }
  
  standUp() {
    this.playerState.isCrouching = false;
  }
  
  punch() {
    if (!this.playerState.isPunching && !this.playerState.isKicking) {
      this.playerState.isPunching = true;
      this.animationTimer = 0; // Reset animation timer for attack
    }
  }
  
  kick() {
    if (!this.playerState.isPunching && !this.playerState.isKicking) {
      this.playerState.isKicking = true;
      this.animationTimer = 0; // Reset animation timer for attack
    }
  }
  
  takeDamage(amount: number) {
    if (!this.playerState.isInvulnerable) {
      this.playerState.isHurt = true;
      this.playerState.isInvulnerable = true;
      this.playerState.invulnerabilityTimer = 1000; // 1 second of invulnerability
      
      // Knockback
      this.velocityX = this.direction === 'right' ? -5 : 5;
      this.velocityY = -5;
      
      // Reset attack states
      this.playerState.isPunching = false;
      this.playerState.isKicking = false;
      
      // Return true if damage was taken
      return true;
    }
    
    // Return false if no damage was taken (invulnerable)
    return false;
  }
  
  getAttackBox() {
    // Return the attack hitbox based on current state
    let attackBox = { x: this.x, y: this.y, width: 0, height: 0 };
    
    if (this.playerState.isPunching) {
      // Punch has shorter range but is faster
      if (this.direction === 'right') {
        attackBox = {
          x: this.x + this.width,
          y: this.y + this.height * 0.3,
          width: 30,
          height: this.height * 0.4
        };
      } else {
        attackBox = {
          x: this.x - 30,
          y: this.y + this.height * 0.3,
          width: 30,
          height: this.height * 0.4
        };
      }
    } else if (this.playerState.isKicking) {
      // Kick has longer range but is slower
      if (this.direction === 'right') {
        attackBox = {
          x: this.x + this.width,
          y: this.y + this.height * 0.5,
          width: 50,
          height: this.height * 0.3
        };
      } else {
        attackBox = {
          x: this.x - 50,
          y: this.y + this.height * 0.5,
          width: 50,
          height: this.height * 0.3
        };
      }
    }
    
    return attackBox;
  }
  
  isAttacking() {
    return this.playerState.isPunching || this.playerState.isKicking;
  }
}
